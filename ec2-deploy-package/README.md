# UOWN Maintenance Page - EC2 Deployment

This package contains everything needed to deploy the UOWN maintenance page on EC2 with automatic HTTPS via Caddy.

## Prerequisites

1. **EC2 Instance Setup:**
   - ARM64 instance (t4g.micro, t4g.small recommended)
   - Docker and Docker Compose installed
   - Security group allowing ports 22, 80, 443

2. **DNS Setup:**
   - A-record pointing your domain to the EC2 public IP

## Quick Deployment

1. **Copy this package to your EC2 instance:**
   ```bash
   scp -i /Users/<USER>/Downloads/staging_db_connector.pem -r ec2-deploy-package/ <EMAIL>:~/
   ```

2. **SSH into EC2 and deploy:**
   ```bash
   ssh -i /Users/<USER>/Downloads/staging_db_connector.pem <EMAIL>
   cd ec2-deploy-package
   chmod +x deploy.sh
   ./deploy.sh maintenance.yourdomain.com
   ```

## Files Included

- `docker-compose.yml` - Docker Compose configuration
- `Caddyfile.template` - Caddy configuration template
- `deploy.sh` - Deployment script
- `README.md` - This file

## Updating the Application

When you have a new version:

1. **Build and push locally:**
   ```bash
   docker buildx build --platform linux/arm64 -t uownco/uown-maintenance:latest --push .
   ```

2. **Update on EC2:**
   ```bash
   cd ec2-deploy-package
   docker-compose pull
   docker-compose up -d
   ```

## Troubleshooting

- **Check logs:** `docker-compose logs -f`
- **Restart services:** `docker-compose restart`
- **Check DNS:** `nslookup your-domain.com`
- **Verify certificates:** `docker-compose logs caddy`
