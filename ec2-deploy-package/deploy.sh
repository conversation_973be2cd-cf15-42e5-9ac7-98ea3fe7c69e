#!/bin/bash

# EC2 deployment script for UOWN Maintenance Page with Caddy HTTPS
set -e

DOMAIN=${1:-""}

if [ -z "$DOMAIN" ]; then
    echo "Usage: $0 <your-domain.com>"
    echo "Example: $0 maintenance.yourdomain.com"
    exit 1
fi

echo "Starting deployment for domain: $DOMAIN"

# Create Caddyfile from template
echo "Creating Caddyfile for domain: $DOMAIN"
sed "s/DOMAIN_PLACEHOLDER/$DOMAIN/g" Caddyfile.template > Caddyfile

echo "Generated Caddyfile:"
cat Caddyfile
echo ""

# Stop and remove any existing deployment
echo "Stopping and removing existing containers..."
docker-compose down 2>/dev/null || true

# Also stop and remove any standalone containers that might be running
echo "Stopping standalone containers (if any)..."
docker stop uown-maintenance-app caddy-proxy 2>/dev/null || true
docker rm uown-maintenance-app caddy-proxy 2>/dev/null || true

# Clean up any orphaned containers
echo "Removing orphaned containers..."
docker-compose down --remove-orphans 2>/dev/null || true

# Pull latest images
echo "Pulling latest images from Docker Hub..."
docker-compose pull

# Start the services
echo "Starting services with Caddy..."
docker-compose up -d

echo ""
echo "🎉 Deployment completed successfully!"
echo "📋 Services started:"
echo "   - Maintenance page: uown-maintenance-app"
echo "   - Caddy proxy: caddy-proxy"
echo "   - Image: uownco/uown-maintenance:latest"
echo ""
echo "🌐 Your maintenance page will be available at:"
echo "   - https://$DOMAIN (HTTPS - automatic certificate)"
echo "   - http://$DOMAIN (HTTP - redirects to HTTPS)"
echo ""
echo "⏳ Note: HTTPS certificate generation may take a few minutes on first deployment."
echo "📊 Check logs with: docker-compose logs -f"
echo ""
echo "🔧 Useful commands:"
echo "   docker-compose ps              # Check service status"
echo "   docker-compose logs -f         # View all logs"
echo "   docker-compose logs -f caddy   # View Caddy logs only"
echo "   docker-compose restart         # Restart services"
echo "   docker-compose down            # Stop all services"
