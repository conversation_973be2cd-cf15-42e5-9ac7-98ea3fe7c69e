/* Animations */

@keyframes bubble-1-rotate {
  0% {
    transform: translate(-74px, 5px);
  }
  33% {
    transform: translate(74px, -40px);
  }
  66% {
    transform: translate(118px, 27px);
  }
  100% {
    transform: translate(-74px, 5px);
  }
}

@keyframes bubble-2-rotate {
  0% {
    transform: translate(114px, 5px);
  }
  33% {
    transform: translate(-55px, 60px);
  }
  66% {
    transform: translate(38px, -25px);
  }
  100% {
    transform: translate(114px, 5px);
  }
}

@keyframes bubble-3-rotate {
  0% {
    transform: translate(39px, -30px);
  }
  33% {
    transform: translate(-74px, 39px);
  }
  66% {
    transform: translate(-117px, -30px);
  }
  100% {
    transform: translate(39px, -30px);
  }
}

/* Bubbles */

.bubble {
  animation: bubble-1-rotate 40s infinite linear;
  background-color: #6adaa9;
  border-radius: 100%;
  bottom: -540px;
  height: 900px;
  left: -300px;
  opacity: 0.8;
  position: absolute;
  width: 900px;
  z-index: 1;
}

.bubble-2 {
  animation: bubble-2-rotate 40s infinite linear;
  bottom: auto;
  height: 400px;
  left: auto;
  opacity: 0.24;
  right: 130px;
  top: -180px;
  width: 400px;
}

.bubble-3 {
  animation: bubble-3-rotate 40s infinite linear;
  bottom: auto;
  height: 220px;
  left: 200px;
  top: 160px;
  width: 220px;
}

@media screen and (max-width: 991px) {
  .bubble-2 {
    top: -100px;
    right: 40px;
    width: 300px;
    height: 300px;
  }
}
