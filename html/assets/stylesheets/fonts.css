@font-face {
  font-family: 'Circular book';
  src: url('/assets/fonts/hinted-CircularStd-Book.eot');
  src: url('/assets/fonts/hinted-CircularStd-Book.eot?#iefix') format('embedded-opentype'),
       url('/assets/fonts/hinted-CircularStd-Book.woff2') format('woff2'),
       url('/assets/fonts/hinted-CircularStd-Book.ttf') format('truetype'),
       url('/assets/fonts/hinted-CircularStd-Book.woff') format('woff'),
       url('/assets/fonts/hinted-CircularStd-Book.svg#circularstd_book') format('svg');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'EB Garamond';
  src: url('/assets/fonts/EBGaramond-Regular.eot');
  src: url('/assets/fonts/EBGaramond-Regular.eot?#iefix') format('embedded-opentype'),
       url('/assets/fonts/EBGaramond-Regular.woff2') format('woff2'),
       url('/assets/fonts/EBGaramond-Regular.woff') format('woff'),
       url('/assets/fonts/EBGaramond-Regular.ttf') format('truetype'),
       url('/assets/fonts/EBGaramond-Regular.svg#EBGaramond-Regular') format('svg');
  font-weight: normal;
  font-style: normal;
}
