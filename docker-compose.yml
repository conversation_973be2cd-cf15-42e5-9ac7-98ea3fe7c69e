version: '3.8'

services:
  maintenance:
    image: uownco/uown-maintenance:latest
    container_name: uown-maintenance-app
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - web

  caddy:
    image: caddy:2-alpine
    container_name: caddy-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - web

networks:
  web:
    driver: bridge

volumes:
  caddy_data:
  caddy_config:
