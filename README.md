# UOWN Maintenance Page

This is a static nginx image which serves the maintenance page for the app.uown.co website.

## Deployment Options

This repository provides two deployment approaches:

### 🚀 **Simple Deployment** (main branch)
- Basic HTTP-only deployment
- Single Docker container
- Minimal setup for development/testing
- Use `deploy-simple.sh` script

### 🔒 **Standalone Deployment** (standalone-deployment branch)
- Production-ready with automatic HTTPS
- Caddy reverse proxy with Let's Encrypt
- Enhanced security headers
- ARM64 optimized
- Use `deploy.sh <domain>` script

---

## Quick Start

### Simple Deployment (HTTP only)
```bash
git checkout main
./deploy-simple.sh
```
Access at: `http://your-server-ip`

### Standalone Deployment (HTTPS)
```bash
git checkout standalone-deployment
./deploy.sh your-domain.com
```
Access at: `https://your-domain.com`

---

## Detailed Setup Instructions

### Prerequisites
- Docker and Docker Compose installed
- Domain name pointing to your server (for HTTPS)
- Ports 80 and 443 open in firewall/security groups

### Simple Deployment Setup

1. **Clone and checkout main branch:**
   ```bash
   git clone <repository-url>
   cd uown-maintenance
   git checkout main
   ```

2. **Deploy:**
   ```bash
   ./deploy-simple.sh
   ```

3. **Access:** Visit `http://your-server-ip`

### Standalone Deployment Setup

1. **Clone and checkout standalone branch:**
   ```bash
   git clone <repository-url>
   cd uown-maintenance
   git checkout standalone-deployment
   ```

2. **Configure your domain:**
   Make sure your domain DNS A record points to your server's IP address.

3. **Deploy with your domain:**
   ```bash
   ./deploy.sh maintenance.yourdomain.com
   ```

4. **Access:** Visit `https://maintenance.yourdomain.com`
   - HTTPS certificate will be automatically generated
   - HTTP traffic will redirect to HTTPS

### Manual Docker Commands

**Simple deployment:**
```bash
docker pull uownco/uown-maintenance:latest
docker run -d -p 80:80 --name uown-maintenance-app --restart unless-stopped uownco/uown-maintenance:latest
```

**Standalone deployment:**
```bash
# Update Caddyfile with your domain first
docker-compose pull
docker-compose up -d
```

---

## Features

### Simple Deployment
- ✅ Basic nginx static file serving
- ✅ Docker Hub ARM64 image
- ✅ Simple HTTP access
- ✅ Minimal resource usage

### Standalone Deployment
- ✅ All simple deployment features
- ✅ Automatic HTTPS with Let's Encrypt
- ✅ Security headers (HSTS, XSS protection, etc.)
- ✅ Gzip compression
- ✅ Static asset caching
- ✅ www to non-www redirects
- ✅ Enhanced nginx configuration
- ✅ Production-ready setup

---

## Troubleshooting

### Simple Deployment Issues
- **"It works" instead of maintenance page:** Rebuild the Docker image
- **Port conflicts:** Change port mapping in deploy script
- **Container won't start:** Check Docker logs with `docker logs uown-maintenance-app`

### Standalone Deployment Issues
- **HTTPS not working:** Ensure domain points to server and ports 80/443 are open
- **Certificate generation failed:** Check Caddy logs with `docker-compose logs caddy`
- **Services won't start:** Check all services with `docker-compose logs`

### Common Solutions
```bash
# Check running containers
docker ps

# View logs
docker-compose logs -f

# Restart services
docker-compose restart

# Complete reset
docker-compose down
docker-compose up -d
```

---

## Development

### Building Images
```bash
# Build ARM64 image
docker buildx build --platform linux/arm64 -t uownco/uown-maintenance:latest --push .

# Build multi-platform
docker buildx build --platform linux/arm64,linux/amd64 -t uownco/uown-maintenance:latest --push .
```

### GitHub Actions
The repository includes automated builds that create multi-platform images on every push to main.

---

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Docker and Caddy logs
3. Ensure DNS and firewall settings are correct
