version: '3.8'

services:
  maintenance:
    build: .
    container_name: uown-maintenance-app
    restart: unless-stopped
    expose:
      - "80"
    networks:
      - web

  nginx-proxy:
    image: nginxproxy/nginx-proxy:alpine
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - nginx_certs:/etc/nginx/certs
      - nginx_vhost:/etc/nginx/vhost.d
      - nginx_html:/usr/share/nginx/html
    networks:
      - web

  letsencrypt:
    image: nginxproxy/acme-companion
    container_name: letsencrypt
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - nginx_certs:/etc/nginx/certs
      - nginx_vhost:/etc/nginx/vhost.d
      - nginx_html:/usr/share/nginx/html
      - acme_data:/etc/acme.sh
    environment:
      - DEFAULT_EMAIL=<EMAIL>
    networks:
      - web
    depends_on:
      - nginx-proxy

networks:
  web:
    driver: bridge

volumes:
  nginx_certs:
  nginx_vhost:
  nginx_html:
  acme_data:
