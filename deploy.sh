#!/bin/bash

# Deployment script for UOWN Maintenance Page
set -e

DEPLOY_MODE=${1:-"simple"}  # simple, caddy, or nginx-proxy

echo "Starting deployment in $DEPLOY_MODE mode..."

case $DEPLOY_MODE in
  "caddy")
    echo "Deploying with <PERSON><PERSON><PERSON> (automatic HTTPS)..."
    # Make sure to update <PERSON><PERSON><PERSON><PERSON><PERSON> with your domain first!
    docker-compose down 2>/dev/null || true
    docker-compose pull
    docker-compose up -d
    echo "Deployment completed! Check https://your-domain.com"
    ;;

  "nginx-proxy")
    echo "Deploying with nginx-proxy and Let's Encrypt..."
    # Make sure to set VIRTUAL_HOST and LETSENCRYPT_HOST environment variables
    docker-compose -f docker-compose-nginx.yml down 2>/dev/null || true
    docker-compose -f docker-compose-nginx.yml pull
    docker-compose -f docker-compose-nginx.yml up -d
    echo "Deployment completed! HTTPS will be available once certificates are issued."
    ;;

  "simple"|*)
    echo "Deploying simple HTTP version..."
    # Stop and remove existing containers
    docker-compose down 2>/dev/null || true
    docker stop uown-maintenance-app 2>/dev/null || true
    docker rm uown-maintenance-app 2>/dev/null || true

    # Pull latest image from Docker Hub
    echo "Pulling latest image from Docker Hub..."
    docker pull uownco/uown-maintenance:latest

    # Remove old local images (optional cleanup)
    echo "Cleaning up old images..."
    docker image prune -f

    # Run new container
    echo "Starting new container..."
    docker run -d -p 80:80 --name uown-maintenance-app --restart unless-stopped uownco/uown-maintenance:latest

    echo "Deployment completed successfully!"
    echo "Application is running at http://$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'your-server-ip')"
    ;;
esac
