#!/bin/bash

# Standalone deployment script for UOWN Maintenance Page with Caddy HTTPS
set -e

DOMAIN=${1:-""}

if [ -z "$DOMAIN" ]; then
    echo "Usage: $0 <your-domain.com>"
    echo "Example: $0 maintenance.yourdomain.com"
    exit 1
fi

echo "Starting standalone deployment with <PERSON><PERSON><PERSON> for domain: $DOMAIN"

# Update Caddyfile with the provided domain
echo "Updating Caddyfile with domain: $DOMAIN"
cat > Caddyfile << EOF
$DOMAIN {
    reverse_proxy maintenance:80

    # Security headers
    header {
        # Enable HSTS
        Strict-Transport-Security max-age=31536000;
        # Prevent clickjacking
        X-Frame-Options SAMEORIGIN
        # Prevent MIME sniffing
        X-Content-Type-Options nosniff
        # XSS Protection
        X-XSS-Protection "1; mode=block"
    }
}

# Redirect www to non-www (optional)
www.$DOMAIN {
    redir https://$DOMAIN{uri}
}
EOF

# Stop any existing deployment
echo "Stopping existing containers..."
docker-compose down 2>/dev/null || true

# Pull latest images
echo "Pulling latest images..."
docker-compose pull

# Start the services
echo "Starting services with <PERSON>ad<PERSON>..."
docker-compose up -d

echo ""
echo "🎉 Deployment completed successfully!"
echo "📋 Services started:"
echo "   - Maintenance page: uown-maintenance-app"
echo "   - Caddy proxy: caddy-proxy"
echo ""
echo "🌐 Your maintenance page will be available at:"
echo "   - https://$DOMAIN (HTTPS - automatic certificate)"
echo "   - http://$DOMAIN (HTTP - redirects to HTTPS)"
echo ""
echo "⏳ Note: HTTPS certificate generation may take a few minutes on first deployment."
echo "📊 Check logs with: docker-compose logs -f"
