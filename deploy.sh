#!/bin/bash

# Deployment script for UOWN Maintenance Page
set -e

echo "Starting deployment..."

# Pull latest changes (if using git)
if [ -d ".git" ]; then
    echo "Pulling latest changes..."
    git pull origin main
fi

# Stop and remove existing container
echo "Stopping existing container..."
docker stop uown-maintenance-app 2>/dev/null || true
docker rm uown-maintenance-app 2>/dev/null || true

# Remove old image
echo "Removing old image..."
docker rmi uown-maintenance 2>/dev/null || true

# Build new image
echo "Building new image..."
docker build -t uown-maintenance .

# Run new container
echo "Starting new container..."
docker run -d -p 80:80 --name uown-maintenance-app --restart unless-stopped uown-maintenance

echo "Deployment completed successfully!"
echo "Application is running at http://$(curl -s http://***************/latest/meta-data/public-ipv4)"
