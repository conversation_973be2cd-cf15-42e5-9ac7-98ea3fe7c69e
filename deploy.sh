#!/bin/bash

# Standalone deployment script for UOWN Maintenance Page with Caddy HTTPS
set -e

DOMAIN=${1:-""}
BUILD_LOCAL=${2:-"false"}

if [ -z "$DOMAIN" ]; then
    echo "Usage: $0 <your-domain.com> [build-local]"
    echo "Examples:"
    echo "  $0 maintenance.yourdomain.com           # Pull from Docker Hub"
    echo "  $0 maintenance.yourdomain.com true      # Build locally"
    echo "  $0 maintenance.yourdomain.com local     # Build locally"
    exit 1
fi

echo "Starting standalone deployment with Caddy for domain: $DOMAIN"

# Update Caddyfile with the provided domain
echo "Updating Caddyfile with domain: $DOMAIN"
cat > Caddyfile << EOF
$DOMAIN {
    reverse_proxy maintenance:80

    # Security headers
    header {
        # Enable HSTS
        Strict-Transport-Security max-age=31536000;
        # Prevent clickjacking
        X-Frame-Options SAMEORIGIN
        # Prevent MIME sniffing
        X-Content-Type-Options nosniff
        # XSS Protection
        X-XSS-Protection "1; mode=block"
    }
}

# Redirect www to non-www (optional)
www.$DOMAIN {
    redir https://$DOMAIN{uri}
}
EOF

# Stop any existing deployment
echo "Stopping existing containers..."
docker-compose down 2>/dev/null || true
docker-compose -f docker-compose.local.yml down 2>/dev/null || true

# Handle image building/pulling
if [[ "$BUILD_LOCAL" == "true" || "$BUILD_LOCAL" == "local" ]]; then
    echo "Building and starting with local build..."

    # Use the local build compose file
    COMPOSE_FILE="docker-compose.local.yml"

    echo "✅ Using local build configuration"
else
    echo "Pulling latest images from Docker Hub..."

    # Use the regular compose file with Docker Hub images
    COMPOSE_FILE="docker-compose.yml"
    docker-compose pull

    echo "✅ Images pulled from Docker Hub"
fi

# Start the services
echo "Starting services with Caddy using $COMPOSE_FILE..."
docker-compose -f $COMPOSE_FILE up -d

echo ""
echo "🎉 Deployment completed successfully!"
echo "📋 Services started:"
echo "   - Maintenance page: uown-maintenance-app"
echo "   - Caddy proxy: caddy-proxy"
if [[ "$BUILD_LOCAL" == "true" || "$BUILD_LOCAL" == "local" ]]; then
    echo "   - Image: Built locally"
else
    echo "   - Image: Pulled from Docker Hub (uownco/uown-maintenance:latest)"
fi
echo ""
echo "🌐 Your maintenance page will be available at:"
echo "   - https://$DOMAIN (HTTPS - automatic certificate)"
echo "   - http://$DOMAIN (HTTP - redirects to HTTPS)"
echo ""
echo "⏳ Note: HTTPS certificate generation may take a few minutes on first deployment."
echo "📊 Check logs with: docker-compose -f $COMPOSE_FILE logs -f"
