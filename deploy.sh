#!/bin/bash

# Deployment script for UOWN Maintenance Page (Docker Hub version)
set -e

echo "Starting deployment..."

# Stop and remove existing container
echo "Stopping existing container..."
docker stop uown-maintenance-app 2>/dev/null || true
docker rm uown-maintenance-app 2>/dev/null || true

# Pull latest image from Docker Hub
echo "Pulling latest image from Docker Hub..."
docker pull uownco/uown-maintenance:latest

# Remove old local images (optional cleanup)
echo "Cleaning up old images..."
docker image prune -f

# Run new container
echo "Starting new container..."
docker run -d -p 80:80 --name uown-maintenance-app --restart unless-stopped uownco/uown-maintenance:latest

echo "Deployment completed successfully!"
echo "Application is running at http://$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'your-server-ip')"
